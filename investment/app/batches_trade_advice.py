import os
import datetime
import math
import random
import tushare as ts
import pandas as pd
import numpy as np
import requests
from dotenv import load_dotenv


# --- Pushover 发送消息函数 ---
def send_pushover(message):
    """
    使用 Pushover 发送消息。
    请确保环境变量中配置了 PUSHOVER_TOKEN 和 PUSHOOVER_USER。
    """
    token = os.getenv("PUSHOVER_TOKEN")
    user = os.getenv("PUSHOVER_USER")
    if not token or not user:
        print("Pushover token 或 user 未设置，无法发送消息。")
        return
    url = "https://api.pushover.net/1/messages.json"
    data = {
        "token": token,
        "user": user,
        "message": message
    }
    try:
        response = requests.post(url, data=data)
        if response.status_code == 200:
            print("消息已发送")
        else:
            print(f"消息发送失败: {response.text}")
    except Exception as e:
        print(f"Pushover发送异常: {e}")

# --- 数据采集函数（基准周期 + 当前回测周期） ---
def get_baseline_and_current(ts_code_6, n_days, end_date, pro):
    """
    从 Tushare 提取某只股票截止 end_date（包含）的最近 (n_days+5) 个交易日数据，
    按日期升序排列，并分为：
      - baseline_df：前5个交易日（基准周期）
      - current_df：后 n_days 个交易日（当前回测周期）
    """
    # 使用CSV文件中提供的股票代码（已经含后缀）
    ts_code_full = ts_code_6  
    end_date_str = end_date
    end_date_obj = datetime.datetime.strptime(end_date_str, "%Y%m%d").date()
    start_date = (end_date_obj - datetime.timedelta(days=n_days+5+50)).strftime('%Y%m%d')
    
    df_all = pro.daily(ts_code=ts_code_full, start_date=start_date, end_date=end_date_str)
    if df_all.empty:
        return None, None
    df_all = df_all.sort_values('trade_date').reset_index(drop=True)
    if len(df_all) < n_days + 5:
        print(f"股票 {ts_code_full} 数据不足以分割为基准周期和当前周期，使用全部数据作为当前周期")
        return None, df_all
    df_last = df_all.iloc[-(n_days+5):].copy()
    baseline_df = df_last.iloc[:5].copy()    # 前5天为基准周期
    current_df = df_last.iloc[5:].copy()       # 后 n_days 为当前周期
    return baseline_df.sort_values('trade_date').reset_index(drop=True), current_df.sort_values('trade_date').reset_index(drop=True)

# --- 可行Q列表生成函数 ---
def feasible_Q_list(H, L, n_days, max_money):
    """
    生成满足以下约束的 Q 值候选列表（每个 Q 为100股的整数倍）：
      1) Q * H <= max_money
      2) Q * L <= max_money
      3) Q * H <= 10000   （每笔交易金额不超过10000元）
      4) 根据交易日天数 n_days 的限制：
         - if n_days >= 60: Q <= floor((0.18 * max_money) / H)
         - elif n_days >= 20: Q <= floor((0.12 * max_money) / H)
         - else:            Q <= floor((0.06 * max_money) / H)
    """
    if H <= 0 or L <= 0:
        return [100]
    Q_max_hl = min(math.floor(max_money / H), math.floor(max_money / L))
    Q_max_trade = math.floor(10000 / H)
    if n_days >= 60:
        Q_max_days = math.floor((0.2 * max_money) / H)
    elif n_days >= 20:
        Q_max_days = math.floor((0.3 * max_money) / H)
    else:
        Q_max_days = math.floor((0.4 * max_money) / H)
    Q_upper = min(Q_max_hl, Q_max_trade, Q_max_days)
    q_list = [q for q in range(100, Q_upper + 1, 100)]
    if not q_list:
        q_list = [100]
    return q_list

# --- 两组策略回测函数（根据新策略要求修改） ---
def backtest_two_strategies_modified(df,
                                     up_ratio_A, base_A, Q_A,   # 策略A（卖出）参数
                                     down_ratio_B, base_B, Q_B, # 策略B（买入）参数
                                     max_money):
    """
    模拟两组策略并行运行（根据新策略要求）：
      - 策略A（分批卖出）：当价格 >= current_base_A*(1+up_ratio_A)时卖出 Q_A 股
      - 策略B（分批买入）：当价格 <= current_base_B*(1-down_ratio_B)时买入 Q_B 股
      - 两组策略共享同一账户（初始资金 = max_money，持仓 = 0）
    返回：
      final_value: 最终账户总价值
      trade_log:   所有交易记录（标记所属策略及交易类型）
      final_base_A: 当前回测周期结束后，新基准价格，采用当前周期最后5个交易日最高价的平均值
      final_base_B: 当前回测周期结束后，新基准价格，采用当前周期最后5个交易日最低价的平均值
    """
    cash = max_money
    position = 0
    trade_log = []
    
    if len(df) == 0:
        return max_money, trade_log, base_A, base_B
    
    # 初始化策略参考价为用户给定的初始值
    current_base_A = base_A
    current_base_B = base_B

    for i in range(len(df)):
        row = df.iloc[i]
        date = row['trade_date']
        high_price = row['high']
        low_price = row['low']
        
        # 策略A：分批卖出
        if high_price >= current_base_A * (1 + up_ratio_A):
            if position >= Q_A:
                op_amount = high_price * Q_A
                position -= Q_A
                cash += op_amount
                trade_log.append({
                    'date': date,
                    'strategy': 'A_sell',
                    'action': 'sell',
                    'price': high_price,
                    'shares': Q_A,
                    'operation_amount': op_amount,
                    'cash_after': cash,
                    'position_after': position
                })
                current_base_A = high_price

        # 策略B：分批买入
        if low_price <= current_base_B * (1 - down_ratio_B):
            op_amount = low_price * Q_B
            if cash >= op_amount:
                position += Q_B
                cash -= op_amount
                trade_log.append({
                    'date': date,
                    'strategy': 'B_buy',
                    'action': 'buy',
                    'price': low_price,
                    'shares': Q_B,
                    'operation_amount': op_amount,
                    'cash_after': cash,
                    'position_after': position
                })
                current_base_B = low_price

    final_value = cash + position * df.iloc[-1]['close']
    
    # 更新基准价格为当前周期最后5个交易日的平均价
    if len(df) >= 5:
        final_base_A = round(df.iloc[-5:]['high'].mean(), 2)
        final_base_B = round(df.iloc[-5:]['low'].mean(), 2)
    else:
        final_base_A = current_base_A
        final_base_B = current_base_B
    
    return final_value, trade_log, final_base_A, final_base_B

# --- 随机搜索两组策略参数（改进版） ---
def run_two_strategy_random_search_improved(df, n_days, max_money, n_iter=500, base_A_init=None, base_B_init=None, avg_pct_prev=0):
    """
    随机搜索两组策略参数组合，参数包括：
      - 策略A：up_ratio_A ∈ [0.005, 0.03]；单次卖出规模 Q_A 从 feasible_Q_list 得到候选。
      - 策略B：down_ratio_B ∈ [0.005, 0.03]；单次买入规模 Q_B 从 feasible_Q_list 得到候选。
      初始基准价格分别固定为 base_A_init 和 base_B_init。
    随机采样 n_iter 次，返回最终账户价值最高的参数组合、交易日志及最终新基准价格。
    """
    up_ratio_candidates = [x/1000 for x in range(5, 31, 5)]
    down_ratio_candidates = [x/1000 for x in range(5, 31, 5)]
    base_A_candidates = [base_A_init]
    base_B_candidates = [base_B_init]
    
    H = df['high'].max()
    L = df['low'].min()
    q_candidates = feasible_Q_list(H, L, n_days, max_money)
    
    best_value = -999999
    best_params = None
    best_log = []
    final_base_A_best = None
    final_base_B_best = None
    
    for _ in range(n_iter):
        up_ratio_A = random.choice(up_ratio_candidates)
        down_ratio_B = random.choice(down_ratio_candidates)
        base_A = random.choice(base_A_candidates)
        base_B = random.choice(base_B_candidates)
        Q_A = random.choice(q_candidates)
        Q_B = random.choice(q_candidates)
        
        final_value, log, final_base_A, final_base_B = backtest_two_strategies_modified(df,
                                            up_ratio_A, base_A, Q_A,
                                            down_ratio_B, base_B, Q_B,
                                            max_money)
        if final_value > best_value:
            best_value = final_value
            best_params = (up_ratio_A, base_A, Q_A, down_ratio_B, base_B, Q_B)
            best_log = log
            final_base_A_best = final_base_A
            final_base_B_best = final_base_B
    return best_value, best_params, best_log, final_base_A_best, final_base_B_best

# --- 网格搜索两组策略参数（改进版） ---
def run_two_strategy_grid_search_improved(df, n_days, max_money, base_A_init, base_B_init):
    """
    网格搜索：遍历各候选参数组合，返回最终账户价值最高的参数组合、交易日志，
    以及本回测周期结束后更新的策略基准价格（基于当前周期最后5个交易日数据）。
    """
    up_candidates = [x/1000 for x in range(5, 31, 5)]
    down_candidates = [x/1000 for x in range(5, 31, 5)]
    base_A_candidates = [base_A_init]
    base_B_candidates = [base_B_init]
    
    H = df['high'].max()
    L = df['low'].min()
    q_candidates = feasible_Q_list(H, L, n_days, max_money)
    
    best_value = -999999
    best_params = None
    best_log = []
    final_base_A_best = None
    final_base_B_best = None
    
    for up_ratio_A in up_candidates:
        for down_ratio_B in down_candidates:
            for base_A in base_A_candidates:
                for base_B in base_B_candidates:
                    for Q_A in q_candidates:
                        for Q_B in q_candidates:
                            final_val, tlog, final_base_A, final_base_B = backtest_two_strategies_modified(df,
                                                            up_ratio_A, base_A, Q_A,
                                                            down_ratio_B, base_B, Q_B,
                                                            max_money)
                            if final_val > best_value:
                                best_value = final_val
                                best_params = (up_ratio_A, base_A, Q_A, down_ratio_B, base_B, Q_B)
                                best_log = tlog
                                final_base_A_best = final_base_A
                                final_base_B_best = final_base_B
    return best_value, best_params, best_log, final_base_A_best, final_base_B_best, H, L

if __name__ == '__main__':
    load_dotenv()
    tushare_token = os.getenv("tushare_token")
    ts.set_token(tushare_token)
    pro = ts.pro_api()
    
    # 默认参数：回测周期20个交易日，最大持仓50000，结束日期为当前日期（格式YYYYMMDD）
    n_days = 20
    max_money = 50000.0
    end_date = datetime.date.today().strftime("%Y%m%d")
    
    from pathlib import Path
    import glob
    from datetime import timedelta

    # 模型文件目录
    model_dir = Path("/Users/<USER>/Documents/Dev/investment/data/result_trade")
    # 根据当前时间决定加载天数：18:00 前加载最近两个交易日，18:00 后仅加载当日
    current_time = datetime.datetime.now()
    if current_time.hour < 18:
        # 18:00 前获取最近两个交易日文件（使用注释中原有的 get_last_n_trade_dates 逻辑）
        def get_last_n_trade_dates(end_date_str, n, pro):
            trade_cal = pro.trade_cal(
                exchange='',
                start_date=(datetime.datetime.strptime(end_date_str, '%Y%m%d') - timedelta(days=30)).strftime('%Y%m%d'),
                end_date=end_date_str,
                is_open='1'
            )
            trade_cal = trade_cal.sort_values('cal_date', ascending=False).head(n)
            return trade_cal['cal_date'].tolist()
        recent_dates = get_last_n_trade_dates(end_date, 2, pro)
        model_files = []
        for date in recent_dates:
            model_files.extend(glob.glob(str(model_dir / f"{date}_Model_*.csv")))
    else:
        # 18:00 后仅加载当前交易日文件
        model_files = glob.glob(str(model_dir / f"{end_date}_Model_*.csv"))


    dfs = []
    for f in model_files:
        df_temp = pd.read_csv(f)
        if 'ts_code' in df_temp.columns:
            cols_to_use = ['ts_code']
            if 'name' in df_temp.columns:
                cols_to_use.append('name')
            # Extract last 3 chars of filename (without .csv) as model code
            model_code = os.path.basename(f)[:-4][-3:]
            df_temp['model'] = model_code
            dfs.append(df_temp[cols_to_use + ['model']])
    # Combine model files into a single DataFrame, handle case when no files found
    if dfs:
        df_models = pd.concat(dfs).drop_duplicates().reset_index(drop=True)
    else:
        # No model files found, create empty DataFrame with expected columns
        df_models = pd.DataFrame(columns=['ts_code', 'name', 'model'])

    df_hold = pd.read_csv("/Users/<USER>/Documents/Dev/investment/data/Holding_stock_list.csv")
    all_ts_codes = pd.concat([df_models[['ts_code']], df_hold[['ts_code']]]).drop_duplicates().reset_index(drop=True)
    df_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,name')
    holding_df = all_ts_codes.merge(df_basic, on='ts_code', how='left')
    
    # 遍历股票列表
    for idx, row in holding_df.iterrows():
        ts_code = row['ts_code']
        name = row['name']
        output_lines = []
        output_lines.append("===================================")
        output_lines.append(f"股票代码: {ts_code}  |  名称: {name}\n")
        
        previous_df, current_df = get_baseline_and_current(ts_code, n_days, end_date, pro)
        if current_df is None or current_df.empty:
            output_lines.append("数据不足，跳过该股票。")
            send_pushover("\n".join(output_lines))
            continue
        # 获取当前回测周期最后一个交易日的收盘价
        close = current_df.iloc[-1]['close']
        output_lines.append(f"最新收盘价格: {close}\n")
        
        if previous_df is None or previous_df.empty:
            base_A_init = current_df['close'].mean()
            base_B_init = current_df['close'].mean()
            avg_pct_prev = 0
        else:
            base_A_init = round(previous_df['high'].mean(), 2)
            base_B_init = round(previous_df['low'].mean(), 2)
        #output_lines.append(f"基于前5个交易日：策略A初始基准价格 = {base_A_init}, 策略B初始基准价格 = {base_B_init}")
        
        # --- 网格搜索 ---
        best_value_g, best_params_g, best_log_g, final_base_A_g, final_base_B_g, H_val, L_val = run_two_strategy_grid_search_improved(current_df, n_days, max_money, base_A_init, base_B_init)
        profit_g = best_value_g - max_money
        #output_lines.append("\n===== 网格搜索最优结果 =====")
        #output_lines.append(f"数据区间: {current_df.iloc[0]['trade_date']} ~ {current_df.iloc[-1]['trade_date']}")
        #output_lines.append(f"共 {len(current_df)} 个交易日, 最高价 H={H_val:.2f}, 最低价 L={L_val:.2f}")
        #output_lines.append("最优参数：")
        #output_lines.append(f"  策略A（卖出）：上涨卖出幅度 = {best_params_g[0]*100:.2f}%, 首次基准价格 = {best_params_g[1]}, 单次操作规模 = {best_params_g[2]} 股")
        #output_lines.append(f"  策略B（买入）：下跌买入幅度 = {best_params_g[3]*100:.2f}%, 首次基准价格 = {best_params_g[4]}, 单次操作规模 = {best_params_g[5]} 股")
        #output_lines.append(f"最终总资产: {best_value_g:.2f}, 净收益: {profit_g:.2f}")
        #output_lines.append(f"本回测周期结束后，策略A新基准价格 = {final_base_A_g}, 策略B新基准价格 = {final_base_B_g}")
        
        # --- 随机搜索 ---
        n_iter = 500
        best_value_r, best_params_r, best_log_r, final_base_A_r, final_base_B_r = run_two_strategy_random_search_improved(current_df, n_days, max_money, n_iter=n_iter, base_A_init=base_A_init, base_B_init=base_B_init)
        profit_r = best_value_r - max_money
        #output_lines.append(f"\n===== 随机搜索最优结果 (随机采样 {n_iter} 次) =====")
        #if best_params_r is None:
            #output_lines.append("随机搜索未找到可行解.")
        #else:
            #output_lines.append("最优参数：")
            #output_lines.append(f"  策略A（卖出）：上涨卖出幅度 = {best_params_r[0]*100:.2f}%, 首次基准价格 = {best_params_r[1]}, 单次操作规模 = {best_params_r[2]} 股")
            #output_lines.append(f"  策略B（买入）：下跌买入幅度 = {best_params_r[3]*100:.2f}%, 首次基准价格 = {best_params_r[4]}, 单次操作规模 = {best_params_r[5]} 股")
            #output_lines.append(f"最终总资产: {best_value_r:.2f}, 净收益: {profit_r:.2f}")
            #output_lines.append(f"本回测周期结束后，策略A新基准价格 = {final_base_A_r}, 策略B新基准价格 = {final_base_B_r}")
        
        # 最终比较
        if profit_g > profit_r:
            best_params_final = best_params_g
            best_value_final = best_value_g
            final_base_A_final = final_base_A_g
            final_base_B_final = final_base_B_g
            method = "网格搜索"
        else:
            best_params_final = best_params_r
            best_value_final = best_value_r
            final_base_A_final = final_base_A_r
            final_base_B_final = final_base_B_r
            method = "随机搜索"
        
        # 重新计算新周期辅助触发幅度：
        avg_daily_pct = current_df['pct_chg'].abs().mean() / 100  # 如果pct_chg是百分比值则除以100
        #new_multiplier_A = final_base_A_final / final_base_B_final * 2
        #new_down_trigger_A = best_params_final[0] * new_multiplier_A
        new_down_trigger_A = best_params_final[0] + avg_daily_pct
        
        #new_multiplier_B = final_base_B_final / final_base_A_final * 2
        #new_up_trigger_B = best_params_final[3] * new_multiplier_B
        new_up_trigger_B = best_params_final[3] + avg_daily_pct

        #output_lines.append(f"\n建议新波动周期策略（{method}）：")
        #output_lines.append(f"===== 新周期波动策略参数 =====\n\n")
        output_lines.append(f"---- 策略 A 分批卖出 ----")
        output_lines.append(f"区间：最低价 L = {L_val:.2f}，最高价 H = {H_val:.2f}")
        output_lines.append(f"新基准价格 = {final_base_A_final}")
        output_lines.append(f"上涨卖出幅度 = {best_params_final[0]*100:.2f}%")
        output_lines.append(f"单次操作规模 = {best_params_final[2]} 股\n")
        output_lines.append(f"---- 策略 B 分批买入 ----")
        output_lines.append(f"区间：最低价 L = {L_val:.2f}，最高价 H = {H_val:.2f}")
        output_lines.append(f"新基准价格 = {final_base_B_final}")
        output_lines.append(f"下跌买入幅度 = {best_params_final[3]*100:.2f}%")
        output_lines.append(f"单次操作规模 = {best_params_final[5]} 股\n")

        #output_lines.append("策略A（卖出）：")
        #output_lines.append(f"  主上涨卖出幅度 = {best_params_final[0]*100:.2f}%, 新辅助买入幅度 = {new_down_trigger_A*100:.2f}%")
        #output_lines.append(f"  首次基准价格 = {best_params_final[1]}, 单次操作规模 = {best_params_final[2]} 股")
        #output_lines.append("策略B（买入）：")
        #output_lines.append(f"  主下跌买入幅度 = {best_params_final[3]*100:.2f}%, 新辅助卖出幅度 = {new_up_trigger_B*100:.2f}%")
        #output_lines.append(f"  首次基准价格 = {best_params_final[4]}, 单次操作规模 = {best_params_final[5]} 股")
        
        output_lines.append(f"最大持仓规模(每5万资金) = {(100 / L_val * 500) // 100 * 100:.0f} 股")
        output_lines.append(f"该模型前一期5万元投入的期末净收益: {best_value_final - max_money:.2f}")
        #output_lines.append(f"本回测周期结束后，策略A新基准价格 = {final_base_A_final}, 策略B新基准价格 = {final_base_B_final}")
        
        # 发送该股票的结果消息
        message = "\n".join(output_lines)
        send_pushover(message)
        # 同时也输出到终端
        #print(message)

    # ——— 发送“需关注股票”和“已持仓股票”清单 ———
    # 1. 从 df_models 中提取去重的 ts_code，再关联 name
    df_models_unique = df_models[['ts_code', 'model']].drop_duplicates().merge(df_basic, on='ts_code', how='left')
    # 获取需关注股票的最新收盘价
    close_list = []
    for ts in df_models_unique['ts_code']:
        _, current_df_focus = get_baseline_and_current(ts, n_days, end_date, pro)
        if current_df_focus is None or current_df_focus.empty:
            close_price = None
        else:
            close_price = current_df_focus.iloc[-1]['close']
        close_list.append(close_price)
    df_models_unique['close_price'] = close_list
    msg_focus = '需关注股票清单：\n' + '\n'.join(
        f"模型：{row['model']}  {row['ts_code']}  {row['name']}  收盘: {row['close_price']:.2f}"
        for _, row in df_models_unique.iterrows()
    )
    send_pushover(msg_focus)

    # 2. 从 df_hold 中提取去重的 ts_code、cost_price、price，再关联 name
    df_hold_unique = df_hold[['ts_code', 'cost_price', 'price']].drop_duplicates().merge(
        df_basic, on='ts_code', how='left')
    msg_hold = '已持仓股票清单：\n' + '\n'.join(
        f"{row['ts_code']}  {row['name']}  成本: {row['cost_price']:.2f}  收盘: {row['price']:.2f} 盈亏: {((row['price']/row['cost_price'] - 1)*100):.2f}%"
        for _, row in df_hold_unique.iterrows()
    )
    send_pushover(msg_hold)