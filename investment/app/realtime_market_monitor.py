import tushare as ts
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import os
import glob
import requests
import warnings
import subprocess
from dotenv import load_dotenv
import psutil
import time as t
import logging
import sys
import schedule
from concurrent.futures import ThreadPoolExecutor, as_completed, wait
import traceback
from typing import Optional
import signal
from tenacity import retry, stop_after_attempt, wait_fixed

# 配置 logging
logging.basicConfig(
    filename='market_monitor.log',
    level=logging.DEBUG,
    format='%(asctime)s %(levelname)s:%(message)s'
)

console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s %(levelname)s:%(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

warnings.filterwarnings("ignore", category=RuntimeWarning)

load_dotenv()

# 常量定义
MARKET_OPEN_TIME = time(9, 25)
MARKET_CLOSE_TIME = time(15, 0)
MARKET_NOON_BREAK_START = time(11, 30)
MARKET_NOON_BREAK_END = time(13, 0)
DATA_DIR = '/Users/<USER>/Documents/Dev/Investment/data/hangqing'
OUTPUT_DIR = '/Users/<USER>/Documents/Dev/Investment/data/juece'
STOCK_LIST_PATH = '/Users/<USER>/Documents/Dev/Investment/data/Stock_List.csv'
PID_FILE = os.path.join(DATA_DIR, 'amazing_market.pid')
MAX_WORKERS = min(10, os.cpu_count() * 2)
HEALTH_CHECK_INTERVAL = 300
REFERENCE_TIME = time(18, 0)  # 新增参考时间常量
# 从环境变量中读取pushover 消息发送的 token 和 user key
PUSHOVER_TOKEN = os.getenv("PUSHOVER_TOKEN")
PUSHOVER_USER = os.getenv("PUSHOVER_USER")

# 5分钟时间窗口定义
TRADING_TIMES = [
    ('09:30:00', '09:35:00'),
    ('09:35:00', '09:40:00'),
    ('09:40:00', '09:45:00'),
    ('09:45:00', '09:50:00'),
    ('09:50:00', '09:55:00'),
    ('09:55:00', '10:00:00'),
    ('10:00:00', '10:05:00'),
    ('10:05:00', '10:10:00'),
    ('10:10:00', '10:15:00'),
    ('10:15:00', '10:20:00'),
    ('10:20:00', '10:25:00'),
    ('10:25:00', '10:30:00'),
    ('10:30:00', '10:35:00'),
    ('10:35:00', '10:40:00'),
    ('10:40:00', '10:45:00'),
    ('10:45:00', '10:50:00'),
    ('10:50:00', '10:55:00'),
    ('10:55:00', '11:00:00'),
    ('11:00:00', '11:05:00'),
    ('11:05:00', '11:10:00'),
    ('11:10:00', '11:15:00'),
    ('11:15:00', '11:20:00'),
    ('11:20:00', '11:25:00'),
    ('11:25:00', '11:30:00'),
    ('13:00:00', '13:05:00'),
    ('13:05:00', '13:10:00'),
    ('13:10:00', '13:15:00'),
    ('13:15:00', '13:20:00'),
    ('13:20:00', '13:25:00'),
    ('13:25:00', '13:30:00'),
    ('13:30:00', '13:35:00'),
    ('13:35:00', '13:40:00'),
    ('13:40:00', '13:45:00'),
    ('13:45:00', '13:50:00'),
    ('13:50:00', '13:55:00'),
    ('13:55:00', '14:00:00'),
    ('14:00:00', '14:05:00'),
    ('14:05:00', '14:10:00'),
    ('14:10:00', '14:15:00'),
    ('14:15:00', '14:20:00'),
    ('14:20:00', '14:25:00'),
    ('14:25:00', '14:30:00'),
    ('14:30:00', '14:35:00'),
    ('14:35:00', '14:40:00'),
    ('14:40:00', '14:45:00'),
    ('14:45:00', '14:50:00'),
    ('14:50:00', '14:55:00'),
    ('14:55:00', '15:00:00')
]

# 读取股票列表
def load_stock_list():
    return pd.read_csv(STOCK_LIST_PATH)

class MarketMonitor:
    def __init__(self):
        self.pro = self._init_tushare()
        self.stock_codes = pd.DataFrame()
        self.executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)
        self._setup_signal_handlers()
        os.makedirs(DATA_DIR, exist_ok=True)
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        self.is_market_closed = False
        self.stock_list_df = load_stock_list()
        self.futures = []  # 用于追踪所有异步任务

    def _setup_signal_handlers(self):
        """
        设置信号处理器，确保脚本可以优雅地退出
        """
        signal.signal(signal.SIGTERM, self._handle_shutdown)
        signal.signal(signal.SIGINT, self._handle_shutdown)

    def _handle_shutdown(self, signum, frame):
        """
        处理关闭信号，清理资源
        """
        logging.info("接收到关闭信号，开始清理...")
        if not self.executor._shutdown:
            self.executor.shutdown(wait=True)
        if os.path.exists(PID_FILE):
            os.remove(PID_FILE)
        sys.exit(0)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def get_reference_trade_date(self) -> Optional[str]:
        """
        获取参考交易日期
        18:00前返回前一交易日，18:00后返回当前交易日
        """
        current_time = datetime.now()
        
        # 确定是否需要获取前一交易日
        if current_time.time() < REFERENCE_TIME:
            # 获取前一交易日
            date = current_time.date() - timedelta(days=1)
            while True:
                date_str = date.strftime('%Y%m%d')
                cal = self.pro.trade_cal(exchange='', start_date=date_str, end_date=date_str)
                if not cal.empty and cal['is_open'].iloc[0] == 1:
                    logging.info(f"当前时间早于18:00，使用前一交易日: {date_str}")
                    return date_str
                date -= timedelta(days=1)
        else:
            # 获取当前交易日
            date = current_time.date()
            while True:
                date_str = date.strftime('%Y%m%d')
                cal = self.pro.trade_cal(exchange='', start_date=date_str, end_date=date_str)
                if not cal.empty and cal['is_open'].iloc[0] == 1:
                    logging.info(f"当前时间晚于18:00，使用当前交易日: {date_str}")
                    return date_str
                date -= timedelta(days=1)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def _init_tushare(self) -> ts.pro_api:
        tushare_token = os.getenv("tushare_token")
        if not tushare_token:
            raise ValueError("请在 .env 文件中设置 tushare_token")
        ts.set_token(tushare_token)
        return ts.pro_api()

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def load_stock_list(self, trade_date_str: str) -> pd.DataFrame:
        """
        加载股票列表
        根据不同时间段加载对应的股票文件
        """
        # 扫描 result_trade 目录下所有符合模式的文件
        result_dir = '/Users/<USER>/Documents/Dev/investment/data/result_trade'
        # 同时加载当前交易日和前一交易日的文件
        from datetime import datetime, timedelta
        # 解析前一个交易日（简单减一天，如需精确可扩展）
        prev_date_obj = datetime.strptime(trade_date_str, '%Y%m%d').date() - timedelta(days=1)
        prev_date_str = prev_date_obj.strftime('%Y%m%d')
        # 匹配当前交易日和前一交易日的文件
        pattern_today = os.path.join(result_dir, f"{trade_date_str}_Model_*.csv")
        pattern_prev = os.path.join(result_dir, f"{prev_date_str}_Model_*.csv") ## 可注释将忽略前一个交易日数据20250519
        csv_files = glob.glob(pattern_today) + glob.glob(pattern_prev) ## 可注释将忽略前一个交易日数据20250519

        all_stocks = []
        for file_path in csv_files:
            try:
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path)
                    if 'ts_code' in df.columns and not df.empty:
                        all_stocks.append(df)
                        logging.info(f"已加载文件 {file_path} 中的 {len(df)} 只股票。")
            except Exception as e:
                logging.error(f"处理文件 {file_path} 时发生错误: {str(e)}")
                raise

        if not all_stocks:
            return pd.DataFrame()

        combined_stocks = pd.concat(all_stocks, ignore_index=True)
        unique_stocks = combined_stocks.drop_duplicates(subset='ts_code', keep='first')
        logging.info(f"去重后的股票数量: {len(unique_stocks)}")
        return unique_stocks

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def fetch_and_save_tick_data(self, ts_code: str, force_update: bool = False):
        """
        获取并保存tick数据，确保正确合并新旧数据
        """
        try:
            df = ts.realtime_tick(ts_code=ts_code, src='dc')
            if df is None or df.empty:
                logging.warning(f"{ts_code} 没有获取到Tick数据。")
                return

            current_date = datetime.now().strftime('%Y-%m-%d')
            df['date'] = current_date

            # 标准化列名
            df.rename(columns={
                'TIME': 'time', 'PRICE': 'price', 'CHANGE': 'change',
                'VOLUME': 'volume', 'AMOUNT': 'amount', 'TYPE': 'type'
            }, inplace=True)
            df = df[['date', 'time', 'price', 'change', 'volume', 'amount', 'type']]

            # 创建 datetime 列用于排序和合并
            df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
            df.sort_values('datetime', ascending=True, inplace=True)

            csv_file = os.path.join(DATA_DIR, f"{ts_code}.csv")

            # 合并新旧数据
            if os.path.exists(csv_file):
                existing_df = pd.read_csv(csv_file)
                if not existing_df.empty:
                    # 为现有数据添加datetime列
                    existing_df['datetime'] = pd.to_datetime(existing_df['date'] + ' ' + existing_df['time'])
                    
                    # 合并数据，保留所有历史数据
                    combined_df = pd.concat([existing_df, df], ignore_index=True)
                    combined_df.sort_values('datetime', ascending=True, inplace=True)
                    combined_df.drop_duplicates(subset=['datetime'], keep='last', inplace=True)
                else:
                    combined_df = df
            else:
                combined_df = df

            # 保存前移除datetime列
            final_df = combined_df.copy()
            final_df.drop('datetime', axis=1, inplace=True)
            final_df.to_csv(csv_file, index=False)

            logging.info(f"成功更新 {ts_code} 的数据，记录数: {len(final_df)}")
            
            # 计算指标并发送通知
            self.calculate_indicators_and_signals(ts_code, combined_df)

        except Exception as e:
            logging.error(f"处理 {ts_code} 数据时发生错误: {str(e)}")
            logging.error(traceback.format_exc())
            raise

    
    # 交易日18:00 后只采集一遍
    def fetch_market_data_once(self):
        """
        改进的市场数据获取方法，逐只股票采集数据，并在遍历完所有股票后结束脚本运行。
        """
        logging.info("开始逐个股票数据采集...")
        try:
            # 清空之前的futures列表
            self.futures = []

            # 为所有股票逐个创建数据获取任务
            if not self.executor._shutdown:
                for _, row in self.stock_codes.iterrows():
                    ts_code = row['ts_code']
                    
                    # 在获取每只股票的数据时，等待它完成，然后再进行下一只股票
                    future = self.executor.submit(self.fetch_and_save_tick_data, ts_code, True)  # 强制更新
                    self.futures.append(future)

                    # 等待当前股票任务完成
                    try:
                        future.result()  # 获取结果，如果有异常会抛出
                    except Exception as e:
                        logging.error(f"采集 {ts_code} 数据时发生错误: {str(e)}")

            logging.info("所有股票数据采集任务已完成")
        
        except Exception as e:
            logging.error(f"数据采集过程中发生错误: {str(e)}")
            logging.error(traceback.format_exc())
            raise


    def calculate_indicators_and_signals(self, ts_code: str, df: pd.DataFrame):
        try:
            if df.empty:
                logging.warning(f"{ts_code} 数据为空，跳过计算指标。")
                return
            
            # 获取 ts_code 和 name 字段
            stock_info = self.stock_list_df[self.stock_list_df['ts_code'].str.startswith(ts_code)]
            name = stock_info['name'].values[0] if not stock_info.empty else '未知'

            # 将日期和时间转换为 datetime 以便于重采样
            df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
            df.set_index('datetime', inplace=True)

            # 按照日期和时间排序
            df = df.sort_values(by=['datetime'], ascending=True)

            # 使用5分钟时间窗口来生成周期数据
            resampled_df = df.resample('5min').agg({
                'price': ['last', 'max', 'min'],
                'volume': 'sum'
            })
            resampled_df.columns = ['close', 'high', 'low', 'total_volume']
            resampled_df = resampled_df.dropna().reset_index()

            # 重新计算CCI和RSI指标，基于重采样后的数据
            resampled_df['TP'] = ((resampled_df['close'] + resampled_df['high'] + resampled_df['low']) / 3).round(2)
            resampled_df['MA_TP'] = resampled_df['TP'].rolling(window=18).mean().round(2)
            resampled_df['MD'] = resampled_df['TP'].rolling(window=18).apply(lambda x: np.mean(np.abs(x - x.mean())), raw=True).round(2)
            resampled_df['CCI'] = ((resampled_df['TP'] - resampled_df['MA_TP']) / (0.015 * resampled_df['MD'])).round(2)

            delta = resampled_df['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=5, min_periods=1).mean()
            avg_loss = loss.rolling(window=5, min_periods=1).mean()
            rs = avg_gain / avg_loss
            resampled_df['RSI'] = (100 - (100 / (1 + rs))).round(2)

            # 添加新字段 amount_up 统计 change > 0 & type = "买盘" 在窗口时间的 amount之和
            resampled_df['amount_up'] = df[
                (df['change'] > 0) & (df['type'] == '买盘')
            ]['amount'].resample('5min').sum().fillna(0).values

            # 添加新字段 amount_down 统计 change < 0 & type = "卖盘" 在窗口时间的 amount之和
            resampled_df['amount_down'] = df[
                (df['change'] < 0) & (df['type'] == '卖盘')
            ]['amount'].resample('5min').sum().fillna(0).values

            # CSV 文件路径（新）
            sign_recorder_path = '/Users/<USER>/Documents/Dev/investment/data/sign_recorder.csv'

            # 确保文件存在，如果不存在则创建（新）
            if not os.path.exists(sign_recorder_path):
                pd.DataFrame(columns=['datetime', 'ts_code', 'name', 'close', 'type']).to_csv(sign_recorder_path, index=False)

            # 加载现有记录（新）
            sign_recorder_df = pd.read_csv(sign_recorder_path)




            # 添加功能，判断第二条记录的 5 分钟数据记录判断买入卖出信号
            if len(resampled_df) >= 2:
                second_latest_row = resampled_df.iloc[-2]
                latest_row = resampled_df.iloc[-1]
                if not pd.isna(second_latest_row['CCI']) and not pd.isna(second_latest_row['RSI']):
                    existing_records = sign_recorder_df[sign_recorder_df['ts_code'] == ts_code]

                    # 记录最新买卖点
                    def generate_signal(signal_type: str, row, ts_code, name, sign_recorder_df, sign_recorder_path):
                        """
                        添加信号记录并更新 sign_recorder.csv 文件
                        """
                        try:
                            new_record = {
                                'datetime': row['datetime'],
                                'ts_code': ts_code,
                                'name': name,
                                'close': row['close'],
                                'type': signal_type
                            }

                            # 移除同一股票之前的同类信号
                            updated_records = sign_recorder_df[
                                ~((sign_recorder_df['ts_code'] == ts_code) & (sign_recorder_df['type'] == signal_type))
                            ]

                            # 添加新信号记录
                            updated_records = pd.concat([updated_records, pd.DataFrame([new_record])], ignore_index=True)

                            # 保存至文件
                            #updated_records.to_csv(sign_recorder_path, index=False)
                            # 改进：批量写入记录
                            updated_records.to_csv(sign_recorder_path, index=False)

                            # 日志记录
                            logging.info(f"{signal_type} 信号已记录: {new_record}")

                        except Exception as e:
                            logging.error(f"生成信号记录时发生错误: {str(e)}")
                            raise


                    # 计算买卖条件
                    #if not existing_records.empty:
                        
                    last_buy = existing_records[existing_records['type'] == 'buy'].tail(1)
                    last_sell = existing_records[existing_records['type'] == 'sell'].tail(1)
                    last_cost = existing_records[existing_records['type'] == 'cost'].tail(1)



                    # 卖出信号判断
                    if (
                        second_latest_row['CCI'] > 100 and second_latest_row['RSI'] > 80 and
                        (latest_row['CCI'] < second_latest_row['CCI'] or latest_row['RSI'] < second_latest_row['RSI']) and
                        latest_row['amount_up'] <= latest_row['amount_down'] * 1.5
                    ):
                        if last_buy.empty or (
                            second_latest_row['close'] > last_buy['close'].iloc[-1] * 1.02 and
                            second_latest_row['close'] < last_buy['close'].iloc[-1] * 1.05):
                
                            # 读取持仓股票列表
                            holding_stock_path = '/Users/<USER>/Documents/Dev/investment/data/Holding_stock_list.csv'
                            if os.path.exists(holding_stock_path):
                                holding_stock_df = pd.read_csv(holding_stock_path)
                            else:
                                logging.error(f"持仓股票列表文件 {holding_stock_path} 不存在，无法进行检查")
                                return

                            # 检查信号中的 ts_code 是否在持仓列表中
                            if ts_code in holding_stock_df['ts_code'].values:    
                
                                if latest_row['total_volume'] >= second_latest_row['total_volume']:


                                    # 获取最近20个交易日的日线行情数据
                                    df_sell = self.pro.daily(ts_code=ts_code, start_date=(datetime.now() - timedelta(days=40)).strftime('%Y%m%d'), 
                                                        end_date=datetime.now().strftime('%Y%m%d'))
                                    
                                    # 只保留最近20个交易日数据
                                    df_sell = df_sell.tail(20)

                                    # 计算最高价和最低价
                                    max_price = df_sell['high'].max()
                                    min_price = df_sell['low'].min()

                                    # 当前价格为 second_latest_row['close']
                                    current_price = second_latest_row['close']

                                    # 计算hold_level股
                                    hold_level = (1 - (current_price - min_price) / (max_price - min_price)) * (50000 / min_price)




                                    self.send_notification(
                                        f"{second_latest_row['datetime']}\n 卖出信号: {ts_code} {name}\n 卖出价格: {second_latest_row['close']:.2f}"
                                    )
                                    self.send_pushover_message(f"{second_latest_row['datetime']}\n 卖出: {ts_code} {name}\n 价格 {second_latest_row['close']:.2f}, 卖出 {(100 / second_latest_row['close'] * 200) // 100 * 100:.0f} 股\n 单笔标准仓位  {hold_level // 100 * 100:.0f} 股", sound="persistent", devices=["iphone911", "Macbookpro"])  #ipad , "macmini"
                                    if ts_code == "000839.SZ":
                                        self.send_friend_notification(f"{second_latest_row['datetime']}\n 卖出股票: {ts_code} {name}\n 价格不低于: {second_latest_row['close']:.2f}")
                                        #self.send_pushover_message(f"{second_latest_row['datetime']}\n 股票: {ts_code} {name}\n 卖出价格: {second_latest_row['close']:.2f}", sound="climb", devices=["ipad"])
                                #else:
                                    #self.send_notification(
                                        #f"{second_latest_row['datetime']}\n 强卖出信号: {ts_code} {name}\n 卖出价格: {second_latest_row['close']:.2f}"
                                    #)
                                    #self.send_pushover_message(f"{second_latest_row['datetime']}\n 强卖出: {ts_code} {name}\n 价格 {second_latest_row['close']:.2f}, 卖出 {(100 / second_latest_row['close'] * 200) // 100 * 100:.0f} 股", sound="persistent", devices=["iphone911", "Macbookpro"]) # ipad , "macmini"
                                    #if ts_code == "000839.SZ":
                                        #self.send_friend_notification(f"{second_latest_row['datetime']}\n 卖出股票: {ts_code} {name}\n 价格不低于: {second_latest_row['close']:.2f}")
                                        #self.send_pushover_message(f"{second_latest_row['datetime']}\n 股票: {ts_code} {name}\n 卖出价格: {second_latest_row['close']:.2f}", sound="climb", devices=["ipad"])


                            generate_signal('sell', second_latest_row, ts_code, name, sign_recorder_df, sign_recorder_path)

                    # 买入信号判断
                    elif (
                        second_latest_row['CCI'] < -150 and second_latest_row['RSI'] < 20 and
                        latest_row['CCI'] > second_latest_row['CCI'] and latest_row['RSI'] > second_latest_row['RSI'] and
                        latest_row['amount_up'] >= latest_row['amount_down'] * 0.5
                    ):
                        if (last_sell.empty or second_latest_row['close'] < last_sell['close'].iloc[-1] * 0.985) or (
                            last_buy.empty or second_latest_row['close'] < last_buy['close'].iloc[-1] * 1.005):

                            if latest_row['total_volume'] <= second_latest_row['total_volume']:


                                # 获取最近20个交易日的日线行情数据
                                df_buy = self.pro.daily(ts_code=ts_code, start_date=(datetime.now() - timedelta(days=40)).strftime('%Y%m%d'), 
                                                    end_date=datetime.now().strftime('%Y%m%d'))
                                
                                # 只保留最近20个交易日数据
                                df_buy = df_buy.tail(20)

                                # 计算最高价和最低价
                                max_price = df_buy['high'].max()
                                min_price = df_buy['low'].min()

                                # 当前价格为 second_latest_row['close']
                                current_price = second_latest_row['close']

                                # 计算hold_level股
                                hold_level = (1 - (current_price - min_price) / (max_price - min_price)) * (50000 / min_price)



                                self.send_notification(
                                    f"{second_latest_row['datetime']}\n 买入信号: {ts_code} {name}\n 买入价格: {second_latest_row['close']:.2f}"
                                )
                                self.send_pushover_message(f"{second_latest_row['datetime']}\n 买入: {ts_code} {name}\n 价格 {second_latest_row['close']:.2f}, 买入 {(100 / second_latest_row['close'] * 200) // 100 * 100:.0f} 股\n 单笔标准仓位  {hold_level // 100 * 100:.0f} 股", sound="climb", devices=["iphone911", "Macbookpro"])  # ipad , "macmini"
                                if ts_code == "000839.SZ":
                                    self.send_friend_notification(f"{second_latest_row['datetime']}\n 买入股票: {ts_code} {name}\n 价格不高于: {second_latest_row['close']:.2f}")
                                    #self.send_pushover_message(f"{second_latest_row['datetime']}\n 股票: {ts_code} {name}\n 买入价格: {second_latest_row['close']:.2f}", sound="climb", devices=["ipad"])
                            #else:
                                #self.send_notification(
                                #    f"{second_latest_row['datetime']}\n 弱买入信号: {ts_code} {name}\n 买入价格: {second_latest_row['close']:.2f}"
                                #)
                                #self.send_pushover_message(f"{second_latest_row['datetime']}\n 弱买入: {ts_code} {name}\n 价格 {second_latest_row['close'] * 0.995:.2f}, 买入 {(100 / second_latest_row['close'] * 100) // 100 * 100:.0f} 股\n 价格 {second_latest_row['close'] * 0.985:.2f}, 买入 {(100 / second_latest_row['close'] * 100) // 100 * 100:.0f} 股", sound="climb", devices=["iphone911", "Macbookpro"]) # ipad , "macmini"
                                #if ts_code == "000839.SZ":
                                    #self.send_friend_notification(f"{second_latest_row['datetime']}\n 买入股票: {ts_code} {name}\n 价格不高于: {second_latest_row['close']:.2f}")
                                    #self.send_pushover_message(f"{second_latest_row['datetime']}\n 股票: {ts_code} {name}\n 买入价格: {second_latest_row['close']:.2f}", sound="climb", devices=["ipad"])


                            generate_signal('buy', second_latest_row, ts_code, name, sign_recorder_df, sign_recorder_path)




                    # 止损信号判断
                    elif not last_buy.empty:
                        if second_latest_row['close'] < last_cost['close'].iloc[-1] * 0.955 and second_latest_row['close'] > last_cost['close'].iloc[-1] * 0.945:
                            # 读取持仓股票列表
                            holding_stock_path = '/Users/<USER>/Documents/Dev/investment/data/Holding_stock_list.csv'
                            if os.path.exists(holding_stock_path):
                                holding_stock_df = pd.read_csv(holding_stock_path)
                            else:
                                logging.error(f"持仓股票列表文件 {holding_stock_path} 不存在，无法进行检查")
                                return

                            # 检查信号中的 ts_code 是否在持仓列表中
                            if ts_code in holding_stock_df['ts_code'].values:    

                                self.send_notification(f"{second_latest_row['datetime']}\n 止损 {ts_code} {name}\n 价格: {second_latest_row['close']:.2f}")
                                self.send_pushover_message(f"{second_latest_row['datetime']}\n 止损清仓: {ts_code} {name}\n 价格 {second_latest_row['close']:.2f}", sound="climb", devices=["iphone911", "Macbookpro"])  # ipad , "macmini"
                                #if ts_code == "000839.SZ":
                                    #self.send_friend_notification(f"{second_latest_row['datetime']}\n 考虑止损: {ts_code} {name}\n 价格: {second_latest_row['close']:.2f}")
                                    #self.send_pushover_message(f"{second_latest_row['datetime']}\n 股票: {ts_code} {name}\n 买入价格: {second_latest_row['close']:.2f}", sound="climb", devices=["ipad"])

                            #generate_signal('sell')

                    # 止盈信号判断
                    elif not last_buy.empty:
                        if second_latest_row['close'] > last_cost['close'].iloc[-1] * 1.08 and second_latest_row['close'] < last_cost['close'].iloc[-1] * 1.12:
                            # 读取持仓股票列表
                            holding_stock_path = '/Users/<USER>/Documents/Dev/investment/data/Holding_stock_list.csv'
                            if os.path.exists(holding_stock_path):
                                holding_stock_df = pd.read_csv(holding_stock_path)
                            else:
                                logging.error(f"持仓股票列表文件 {holding_stock_path} 不存在，无法进行检查")
                                return

                            # 检查信号中的 ts_code 是否在持仓列表中
                            if ts_code in holding_stock_df['ts_code'].values:    

                                self.send_notification(f"{second_latest_row['datetime']}\n 止盈 {ts_code} {name}\n 价格: {second_latest_row['close']:.2f}")
                                self.send_pushover_message(f"{second_latest_row['datetime']}\n 止盈减仓: {ts_code} {name}\n 价格 {second_latest_row['close']:.2f}", sound="climb", devices=["iphone911", "Macbookpro"]) # , "macmini", "ipad"
                                #if ts_code == "000839.SZ":
                                    #self.send_friend_notification(f"{second_latest_row['datetime']}\n 考虑止损: {ts_code} {name}\n 价格: {second_latest_row['close']:.2f}")
                                    #self.send_pushover_message(f"{second_latest_row['datetime']}\n 股票: {ts_code} {name}\n 买入价格: {second_latest_row['close']:.2f}", sound="climb", devices=["ipad"])

                            #generate_signal('sell')


            # 保存到新的CSV文件
            output_path = os.path.join(OUTPUT_DIR, f"{ts_code}_5min_analysis.csv")
            resampled_df.to_csv(output_path, index=False, mode='w')
            logging.info(f"5分钟行情数据已保存至 {output_path}")

        except Exception as e:
            logging.error(f"计算 {ts_code} 的指标和信号时发生错误: {str(e)}")
            logging.error(traceback.format_exc())

    def send_notification(self, message: str, image_path=None):
        try:
            # 发送系统通知
            subprocess.run(['osascript', '-e', f'display notification "{message}" with title "Trading Signal"'])
            # 发送iMessage
            phone_numbers = os.getenv('phone_number').split(',') if os.getenv('phone_number') else []
            for phone_number in phone_numbers:
                apple_script = f'''
                tell application "Messages"
                    set targetBuddy to buddy "{phone_number}" of (service 1 whose service type = iMessage)
                    set targetService to 1st service whose service type = iMessage
                    set textMessage to "{message}"
                    send textMessage to targetBuddy
                '''
                
                apple_script += '''
                end tell
                '''
                result = subprocess.run(['osascript', '-e', apple_script], capture_output=True, text=True)
                if result.returncode != 0:
                    logging.error(f"Error sending iMessage to {phone_number}: {result.stderr}")
                else:
                    logging.info(f"Notification sent to {phone_number}: {message}")
        except Exception as e:
            logging.error(f"Error sending notification: {str(e)}")

    def send_friend_notification(self, message: str, image_path=None):
        try:
            # 发送系统通知
            subprocess.run(['osascript', '-e', f'display notification "{message}" with title "Trading Signal"'])
            # 发送iMessage
            friend_numbers = os.getenv('friend_number').split(',') if os.getenv('friend_number') else []
            for friend_number in friend_numbers:
                apple_script = f'''
                tell application "Messages"
                    set targetBuddy to buddy "{friend_number}" of (service 1 whose service type = iMessage)
                    set targetService to 1st service whose service type = iMessage
                    set textMessage to "{message}"
                    send textMessage to targetBuddy
                '''
                
                apple_script += '''
                end tell
                '''
                result = subprocess.run(['osascript', '-e', apple_script], capture_output=True, text=True)
                if result.returncode != 0:
                    logging.error(f"Error sending iMessage to {friend_number}: {result.stderr}")
                else:
                    logging.info(f"Notification sent to {friend_number}: {message}")
        except Exception as e:
            logging.error(f"Error sending notification: {str(e)}")

    def send_pushover_message(self, message, sound="climb", devices=None):
        try:
            url = "https://api.pushover.net/1/messages.json"
            
            # 如果传入多个设备，则使用逗号连接
            device_list = ",".join(devices) if devices else ""

            data = {
                "token": PUSHOVER_TOKEN,           # 从环境变量读取 Token
                "user": PUSHOVER_USER,             # 从环境变量读取 User Key
                "message": message,
                "sound": sound,                    # 声音设置为可变参数，默认为 "climb"
                "device": device_list              # 指定设备列表
            }
            response = requests.post(url, data=data)
            if response.status_code == 200:
                logging.info("Pushover 消息发送成功！")
            else:
                logging.error(f"Pushover 消息发送失败: {response.status_code} - {response.text}")
        except requests.RequestException as e:
            logging.error(f"请求 Pushover 消息发送时发生错误: {str(e)}")

    def cleanup_data(self, reference_trade_date_str: str):
        """
        清理历史数据，保留交易日当天的整数据
        """
        try:
            logging.info("开始清理历史数据...")
            for root, _, files in os.walk(DATA_DIR):
                for file in files:
                    file_path = os.path.join(root, file)
                    if file.endswith('.csv'):
                        df = pd.read_csv(file_path)
                        if 'date' in df.columns:
                            df = df[df['date'] == reference_trade_date_str]
                            df.to_csv(file_path, index=False)
                            logging.info(f"成功清理并保留 {file} 的当天数据。")
                        else:
                            logging.warning(f"{file_path} 没有找到'date'列，无法清理。")
        except Exception as e:
            logging.error(f"清理数据时发生错误: {str(e)}")
            logging.error(traceback.format_exc())

    def is_market_open(self):
        now = datetime.now().time()
        return (MARKET_OPEN_TIME <= now < MARKET_NOON_BREAK_START) or (MARKET_NOON_BREAK_END <= now < MARKET_CLOSE_TIME)

    def schedule_jobs(self):
        """
        改进的调度方法，更精确地控制数据采集时间
        """
        logging.info("开始调度数据采集任务...")
        
        while True:
            current_time = datetime.now().time()
            
            # 如果已经收盘，执行最后一次采集然后退出
            if current_time >= MARKET_CLOSE_TIME:
                logging.info("市场收盘，执行最后一次数据采集...")
                self.fetch_market_data()
                break
                
            # 在交易时间内执行数据采集
            if self.is_market_open():
                self.fetch_market_data()
                t.sleep(60)  # 每分钟采集一次数据
            else:
                # 休市期间暂停采集
                logging.info("当前是休市时间，等待下一个交易时段...")
                t.sleep(300)  # 休市期间每5分钟检查一次
                
        logging.info("数据采集调度任务结束...")

    def fetch_market_data(self):
        """
        改进的市场数据获取方法，确保所有数据都被完整处理
        """
        logging.info("开始数据采集...")
        try:
            # 清空之前的futures列表
            self.futures = []
            # 为所有股票创建数据获取任务
            if not self.executor._shutdown:
                for _, row in self.stock_codes.iterrows():
                    ts_code = row['ts_code']
                    future = self.executor.submit(self.fetch_and_save_tick_data, ts_code, True)  # 强制更新
                    self.futures.append(future)
                
                # 等待所有任务完成
                logging.info(f"等待 {len(self.futures)} 个股票的数据处理完成...")
                wait(self.futures)  # 使用wait而不是as_completed，确保所有任务都完成
                
                # 检查是否有任务失败
                failed_tasks = []
                for future in self.futures:
                    try:
                        future.result()  # 获取结果，如果有异常会抛出
                    except Exception as e:
                        failed_tasks.append(str(e))
                
                if failed_tasks:
                    logging.error(f"有 {len(failed_tasks)} 个任务失败: {failed_tasks}")
                else:
                    logging.info("所有数据采集和处理任务已完成")
        
        except Exception as e:
            logging.error(f"数据采集过程中发生错误: {str(e)}")
            logging.error(traceback.format_exc())
            raise

    def run(self):
        """
        改进的运行方法，支持交易时间和18:00后的数据采集
        """
        logging.info("启动市场监控...")
        try:
            # 获取参考交易日期
            reference_trade_date = self.get_reference_trade_date()
            if not reference_trade_date:
                logging.error("无法获取参考交易日期，监控启动失败。")
                sys.exit(1)
                
            # 加载股票列表
            self.stock_codes = self.load_stock_list(reference_trade_date)
            if self.stock_codes.empty:
                logging.error("股票列表为空，未能加载任何股票信息。")
                sys.exit(1)

            current_time = datetime.now().time()
            # 获取今天交易日状态
            current_date = datetime.now().date()
            trade_cal = self.pro.trade_cal(exchange='', start_date=current_date.strftime('%Y%m%d'),
                                           end_date=current_date.strftime('%Y%m%d'))
            
            # 交易时间的数据采集
            if self.is_market_open():
                # 仅在今天为交易日时进行交易时段数据采集
                if trade_cal.empty or trade_cal['is_open'].iloc[0] == 0:
                    logging.info("今天不是交易日，跳过交易时段数据采集。")
                else:
                    logging.info("当前是交易时间，开始数据采集...")
                    self.fetch_market_data()
                    # 如果是开盘时间，继续执行schedule_jobs进行持续采集
                    if MARKET_OPEN_TIME <= current_time < MARKET_CLOSE_TIME:
                        self.schedule_jobs()
            # 18:00 后的数据采集
            elif current_time >= REFERENCE_TIME:
                # 检查明日是否为交易日
                from datetime import timedelta
                tomorrow = datetime.now().date() + timedelta(days=1)
                tomorrow_str = tomorrow.strftime('%Y%m%d')
                cal_tomorrow = self.pro.trade_cal(exchange='', start_date=tomorrow_str, end_date=tomorrow_str)
                if not cal_tomorrow.empty and cal_tomorrow['is_open'].iloc[0] == 1:
                    logging.info(f"明日交易日 ({tomorrow_str})，18:00后进行一次性数据采集...")
                    self.fetch_market_data_once()
                else:
                    logging.info(f"明日非交易日 ({tomorrow_str})，跳过一次性数据采集")

        except Exception as e:
            logging.error(f"程序运行时发生错误: {str(e)}")
            logging.error(traceback.format_exc())
            sys.exit(1)
        finally:
            # 清理资源
            if os.path.exists(PID_FILE):
                os.remove(PID_FILE)
            logging.info("程序执行完毕，退出。")
            sys.exit(0)

if __name__ == "__main__":
    monitor = MarketMonitor()
    monitor.run()
